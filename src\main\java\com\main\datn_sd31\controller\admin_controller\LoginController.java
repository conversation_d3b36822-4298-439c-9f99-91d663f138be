package com.main.datn_sd31.controller.admin_controller;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class LoginController {

    @GetMapping("/admin/dang-nhap")
    public String loginPage(@RequestParam(value = "error", required = false) String error,
                           @RequestParam(value = "logout", required = false) String logout,
                           Model model){

        System.out.println("[LOGIN PAGE] Called with error: " + error + ", logout: " + logout);

        if (error != null) {
            model.addAttribute("errorMessage", error);
            System.out.println("[LOGIN PAGE] Error parameter received: " + error);
            System.out.println("[LOGIN PAGE] Added errorMessage to model: " + error);
        }

        if (logout != null) {
            model.addAttribute("logoutMessage", "Đ<PERSON>ng xuất thành công!");
            System.out.println("[LOGIN PAGE] Added logoutMessage to model");
        }

        return "admin/login";
    }

}
