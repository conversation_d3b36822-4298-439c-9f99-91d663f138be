Simple line icons
====

[![Gitter](https://badges.gitter.im/Join%20Chat.svg)](https://gitter.im/thesabbir/simple-line-icons?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)

Simple line icons with CSS, SAAS, LESS & Web-fonts files.

Preview & Docs
===
[https://thesabbir.github.io/simple-line-icons](https://thesabbir.github.io/simple-line-icons)


Installation
====
via [cdnjs](http://cdnjs.com/libraries/simple-line-icons)
```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.css">
```
via [bower](http://bower.io/search/?q=simple-line-icons)

```shell

bower install simple-line-icons --save

```
via [npm](https://www.npmjs.com/package/simple-line-icons)

```shell

npm install simple-line-icons --save

```

Or, you can also clone or [download this repository](https://github.com/thesabbir/simple-line-icons/archive/master.zip) as zip.


If you are a designers you can use [this creative cloud library](http://adobe.ly/2bQ48wl).

Customizing LESS/SASS variables
====

### LESS:

```less
@simple-line-font-path        : "/path/to/font/files";
@simple-line-font-family      : "desired-name-font-family";
@simple-line-icon-prefix      : prefix-;
```

### SASS:

```sass
$simple-line-font-path        : "/path/to/font/files";
$simple-line-font-family      : "desired-name-font-family";
$simple-line-icon-prefix      : "prefix-";
```


Credits
===
[Jamal Jama](https://twitter.com/byjml) for creating this awesome webfont & [Ahmad Firoz](https://twitter.com/firoz_usf) for extending it further.

Contributors
====
[Check Here](https://github.com/thesabbir/simple-line-icons/graphs/contributors)

Contributions
====
Contributions are more then welcome. Keep them coming!
Please make sure you have read our [guide line](/CONTRIBUTING.md).

License
====
You're free to use the web-font in a template/theme intended for sale on marketplaces like Themeforest.

CSS, SCSS & LESS files are under [MIT License](/LICENSE.md).
