package com.main.datn_sd31.security;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.*;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class CustomAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException, ServletException {
        String error = "Lỗi đăng nhập!";
        if (exception instanceof BadCredentialsException) {
            error = "Mật khẩu không đúng!";
        } else if (exception instanceof UsernameNotFoundException) {
            error = exception.getMessage();
        } else if (exception instanceof DisabledException) {
            error = "Tài khoản đã bị vô hiệu hóa!";
        } else if (exception instanceof LockedException) {
            error = "Tài khoản đã bị khóa!";
        } else if (exception instanceof AccountExpiredException) {
            error = "Tài khoản đã hết hạn!";
        } else if (exception instanceof CredentialsExpiredException) {
            error = "Mật khẩu đã hết hạn!";
        } else {
            error = exception.getMessage();
        }

        // Log lỗi đăng nhập vào console
        String timestamp = LocalDateTime.now().format(formatter);
        String clientIP = getClientIP(request);

        System.out.println("=== LOGIN FAILURE ===");
        System.out.println("Time: " + timestamp);
        System.out.println("URI: " + request.getRequestURI());
        System.out.println("Client IP: " + clientIP);
        System.out.println("Username: " + request.getParameter("username"));
        System.out.println("Error: " + error);
        System.out.println("Exception: " + exception.getClass().getSimpleName());
        System.out.println("Exception Message: " + exception.getMessage());
        System.out.println("===================");

        // Xác định trang login phù hợp dựa trên request
        String loginPage = "/admin/dang-nhap";
        String uri = request.getRequestURI();
        if (uri != null && uri.contains("/khach-hang")) {
            loginPage = "/khach-hang/dang-nhap";
        }

        // Redirect về trang login với thông báo lỗi
        response.sendRedirect(loginPage + "?error=" + java.net.URLEncoder.encode(error, "UTF-8"));
    }

    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0];
    }
}