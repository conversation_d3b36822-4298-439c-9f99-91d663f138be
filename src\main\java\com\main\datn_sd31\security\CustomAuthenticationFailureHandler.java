package com.main.datn_sd31.security;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.*;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
public class CustomAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomAuthenticationFailureHandler.class);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException, ServletException {
        String error = "Lỗi đăng nhập!";
        if (exception instanceof BadCredentialsException) {
            error = "Mật khẩu không đúng!";
        } else if (exception instanceof UsernameNotFoundException) {
            error = exception.getMessage();
        } else if (exception instanceof DisabledException) {
            error = "Tài khoản đã bị vô hiệu hóa!";
        } else if (exception instanceof LockedException) {
            error = "Tài khoản đã bị khóa!";
        } else if (exception instanceof AccountExpiredException) {
            error = "Tài khoản đã hết hạn!";
        } else if (exception instanceof CredentialsExpiredException) {
            error = "Mật khẩu đã hết hạn!";
        } else {
            error = exception.getMessage();
        }

        // Log chi tiết về lỗi đăng nhập
        String timestamp = LocalDateTime.now().format(formatter);
        String clientIP = getClientIP(request);
        String userAgent = request.getHeader("User-Agent");

        logger.error("=== LOGIN FAILURE ===");
        logger.error("Time: {}", timestamp);
        logger.error("URI: {}", request.getRequestURI());
        logger.error("Client IP: {}", clientIP);
        logger.error("User Agent: {}", userAgent);
        logger.error("Username: {}", request.getParameter("username"));
        logger.error("Error: {}", error);
        logger.error("Exception: {}", exception.getClass().getSimpleName());
        logger.error("Exception Message: {}", exception.getMessage());
        logger.error("===================");

        // Console log để debug nhanh
        System.out.println("[LOGIN FAIL] " + timestamp + " - URI: " + request.getRequestURI());
        System.out.println("[LOGIN FAIL] ERROR: " + error);
        System.out.println("[LOGIN FAIL] Exception: " + exception.getClass().getSimpleName());

        // Xác định trang login phù hợp dựa trên request
        String loginPage = "/admin/dang-nhap";
        String uri = request.getRequestURI();
        if (uri != null && uri.contains("/khach-hang")) {
            loginPage = "/khach-hang/dang-nhap";
        }

        // Redirect với thông báo lỗi
        String redirectUrl = loginPage + "?error=" + java.net.URLEncoder.encode(error, "UTF-8");
        System.out.println("[LOGIN FAIL] Redirecting to: " + redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    private String getClientIP(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null) {
            return request.getRemoteAddr();
        }
        return xfHeader.split(",")[0];
    }
}