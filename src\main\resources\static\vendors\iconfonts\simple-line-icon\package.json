{"_args": [["simple-line-icons@2.4.1", "/var/www/html/star_pro"]], "_from": "simple-line-icons@2.4.1", "_id": "simple-line-icons@2.4.1", "_inCache": true, "_installable": true, "_location": "/simple-line-icons", "_nodeVersion": "6.2.1", "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/simple-line-icons-2.4.1.tgz_1474471878001_0.22512383852154016"}, "_npmUser": {"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON>ir"}, "_npmVersion": "3.10.3", "_phantomChildren": {}, "_requested": {"name": "simple-line-icons", "raw": "simple-line-icons@2.4.1", "rawSpec": "2.4.1", "scope": null, "spec": "2.4.1", "type": "version"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/simple-line-icons/-/simple-line-icons-2.4.1.tgz", "_shasum": "b75bc5a0d87e530928c2ccda5735274bb256f234", "_shrinkwrap": null, "_spec": "simple-line-icons@2.4.1", "_where": "/var/www/html/star_pro", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/thesabbir/simple-line-icons/issues"}, "dependencies": {}, "description": "Simple and elegent line icons.", "devDependencies": {}, "directories": {}, "dist": {"shasum": "b75bc5a0d87e530928c2ccda5735274bb256f234", "tarball": "https://registry.npmjs.org/simple-line-icons/-/simple-line-icons-2.4.1.tgz"}, "gitHead": "ae1cf4ec75a8a32c4810a2fe7ca8c2fdf6f3e32c", "homepage": "https://thesabbir.github.io/simple-line-icons", "keywords": ["css", "icon", "line", "simple", "webfont"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "<PERSON><PERSON><PERSON>ir", "email": "<EMAIL>"}], "name": "simple-line-icons", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+https://github.com/thesabbir/simple-line-icons.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "2.4.1"}