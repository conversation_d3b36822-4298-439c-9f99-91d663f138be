<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Đăng nhập Admin</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- External CSS -->
    <link rel="stylesheet" th:href="@{/css/admin-login.css}">
    <!-- <link rel="stylesheet" th:href="@{/css/login-error.css}"> -->
    <style>
        .login-error-message {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 5px;
        }

        .error-text {
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
<!-- Animated background particles -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<div class="login-container">
    <div class="login-header">
        <div class="admin-icon">👨‍💼</div>
        <h1>Admin Login</h1>
        <p>Đăng nhập vào hệ thống quản trị</p>
    </div>

    <form th:action="@{/admin/dang-nhap}" method="post" id="loginForm">
        <div class="form-group">
            <label for="username">Email</label>
            <input type="email" id="username" name="username" placeholder="Nhập email của bạn" required />
        </div>

        <div class="form-group">
            <label for="password">Mật khẩu</label>
            <input type="password" id="password" name="password" placeholder="Nhập mật khẩu" required />
        </div>

        <!-- ✅ CSRF token bắt buộc -->
        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

        <button type="submit" class="login-btn">
            Đăng nhập
        </button>
    </form>
    <div th:if="${param.error}" class="login-error-message">
        <span class="error-text" th:text="${param.error}"></span>
    </div>
</div>
</div>

<script th:src="@{/js/admin-login.js}"></script>
</body>
</html>
