<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <title><PERSON><PERSON>ng nhập Admin</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- External CSS -->
    <link rel="stylesheet" th:href="@{/css/admin-login.css}">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .error-message, .success-message {
            margin-top: 15px;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .alert {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .alert i {
            margin-right: 8px;
        }
    </style>
</head>
<body>
<!-- Animated background particles -->
<div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
</div>

<div class="login-container">
    <div class="login-header">
        <div class="admin-icon">👨‍💼</div>
        <h1>Admin Login</h1>
        <p>Đăng nhập vào hệ thống quản trị</p>
    </div>

    <form th:action="@{/admin/dang-nhap}" method="post" id="loginForm">
        <div class="form-group">
            <label for="username">Email</label>
            <input type="email" id="username" name="username" placeholder="Nhập email của bạn" required />
        </div>

        <div class="form-group">
            <label for="password">Mật khẩu</label>
            <input type="password" id="password" name="password" placeholder="Nhập mật khẩu" required />
        </div>

        <!-- ✅ CSRF token bắt buộc -->
        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

        <button type="submit" class="login-btn">
            Đăng nhập
        </button>
    </form>

    <!-- Hiển thị thông báo lỗi từ URL parameter -->
    <div th:if="${param.error}" class="error-message">
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Đăng nhập thất bại!</strong>
            <br>
            <span th:text="${param.error}">Sai email hoặc mật khẩu!</span>
        </div>
    </div>

    <!-- Hiển thị thông báo lỗi từ model -->
    <div th:if="${errorMessage}" class="error-message">
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Đăng nhập thất bại!</strong>
            <br>
            <span th:text="${errorMessage}">Sai email hoặc mật khẩu!</span>
        </div>
    </div>

    <!-- Hiển thị thông báo đăng xuất thành công -->
    <div th:if="${param.logout}" class="success-message">
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i>
            <strong>Đăng xuất thành công!</strong>
        </div>
    </div>

    <!-- Hiển thị thông báo đăng xuất từ model -->
    <div th:if="${logoutMessage}" class="success-message">
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i>
            <strong th:text="${logoutMessage}">Đăng xuất thành công!</strong>
        </div>
    </div>
</div>

<script th:src="@{/js/admin-login.js}"></script>
</body>
</html>
