# Contributing to Simple Line Icons

First off, thanks for taking the time to contribute!

The following is a set of guidelines for contributing to Simple Line Icons. These are just guidelines, not rules, use your best judgment and feel free to propose changes to this document in a pull request.

## Code of Conduct

This project adheres to the Contributor Covenant [code of conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code. Please report unacceptable <NAME_EMAIL>.

## How Can I Contribute?

 - [Reporting bugs](#reporting-bugs)
 - [Suggestion enhancement](#suggestion-enhancement)
 - [Pull request](#pull-request)
 - [Improving documentations](#documentations)


## Reporting bugs

Bugs are tracked as GitHub issues. Explain the problem and include additional details to help maintainers reproduce the problem.

 - Use a clear and descriptive title for the issue to identify the problem.
 - Describe the exact steps which reproduce the problem.
 - Explain which behavior you expected to see instead and why.
 - For visual problems include screenshots which clearly demonstrates the problem.

Do not forget to mention:

 - Which version of the package are you using.
 - If the issue is related to installation please mention which method or package manager did you use.

## Suggestion Enhancement

Before creating enhancement suggestions, please check out if a proposal already exists.

 - Mention why you this enhancement is useful.
 - You can list some other packages where this enhancement exists.


## Pull Request

Pull requests are always welcome. We love those!

  - Include screenshot/image if you have added or modified new icon.
  - We follow [github flow](https://guides.github.com/introduction/flow/) for our development model.
  - Nice commit messages are always helpful.
  - Reference issues and pull requests liberally.
  - If you changed any one styleheet please make sure it reflects all styles file(LESS, CSS, SCSS).

## Documentations

Documentation is currently generated using `generate.php` file from `gh-pages` brunch.

  - Modify `generate.php`.
  - Run `php generate.php > index.html`.
  - Do not edit `index.html`.
  - Documentation should be up-to-date with master.

  ___

  Unsure where to begin contributing? You can start by looking through these beginner, help-wanted, enhancement labels issues.

  You can [checkout our very cool contributors graph now](https://github.com/thesabbir/simple-line-icons/graphs/contributors).
